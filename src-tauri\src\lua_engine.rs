use mlua::{<PERSON><PERSON>, Result as <PERSON><PERSON><PERSON><PERSON><PERSON>, Table, Value};
use serde::{Deserialize, Serialize};
use std::sync::{Arc, Mutex};

// Helper function to get layer-specific colors
fn get_layer_color(layer_name: &str) -> String {
    match layer_name {
        // Panel and structural layers
        "PANEL" => "#8B4513".to_string(),

        // Cutting operations (red tones)
        layer if layer.contains("H_Freze") && layer.contains("_SF") => "#DC143C".to_string(), // Bottom face cuts
        layer if layer.contains("H_Freze") => "#FF4500".to_string(), // Top face cuts
        layer if layer.contains("K_Freze") && layer.contains("_SF") => "#B22222".to_string(), // Bottom face grooves
        layer if layer.contains("K_Freze") => "#FF6347".to_string(), // Top face grooves

        // V-bit operations (blue tones)
        layer if layer.contains("K_AciliV") => "#4169E1".to_string(),
        layer if layer.contains("V45") => "#0000FF".to_string(),
        layer if layer.contains("V30") => "#1E90FF".to_string(),
        layer if layer.contains("V60") => "#0066CC".to_string(),

        // Special operations
        "Cep_Acma" => "#FF1493".to_string(),
        "CLEANCORNERS" => "#9932CC".to_string(),
        "CLEANUP" => "#FF1493".to_string(),
        "DEEPEND" => "#DC143C".to_string(),
        "DEEPFRAME" => "#B22222".to_string(),
        "THINFRAME" => "#CD853F".to_string(),

        // Measurement and markup layers (gray tones)
        layer if layer.starts_with("LMM") => "#808080".to_string(),
        layer if layer.starts_with("LUA") => "#696969".to_string(),

        // Default colors
        "default" => "#000000".to_string(),
        _ => {
            // Try to extract numeric diameter for color generation
            if let Some(start) = layer_name.find(char::is_numeric) {
                let num_str: String = layer_name[start..].chars()
                    .take_while(|c| c.is_numeric())
                    .collect();
                if let Ok(diameter) = num_str.parse::<u32>() {
                    let hue = (diameter * 30) % 360;
                    return format!("hsl({}, 70%, 50%)", hue);
                }
            }
            "#666666".to_string()
        }
    }
}

// Bundle Lua libraries as static strings - Updated to use new_engine folder
const ADEKO_LIB_LUA: &str = include_str!("../../LIBRARY/luaLibrary/new_engine/ADekoLib.lua");
const MAKERJS_ENGINE_LUA: &str = include_str!("../../LIBRARY/luaLibrary/new_engine/makerjs_engine.lua");
const ADEKO_DEBUG_MODE_LUA: &str = include_str!("../../LIBRARY/luaLibrary/new_engine/ADekoDebugMode.lua");

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DrawCommand {
    pub command_type: String,
    pub x1: f64,
    pub y1: f64,
    pub x2: f64,
    pub y2: f64,
    pub z1: Option<f64>, // Z coordinate for start point of 3D lines
    pub z2: Option<f64>, // Z coordinate for end point of 3D lines
    pub radius: f64,
    pub color: String,
    pub size: f64,
    pub text: String,
    pub layer_name: String,
    pub thickness: Option<f64>, // Thickness/extrusion for 3D representation
    pub face: Option<String>, // Face information from setFace() calls: "top", "bottom", "left", "right", "front", "rear"
    pub start_angle: Option<f64>, // Start angle for arcs in degrees
    pub end_angle: Option<f64>,   // End angle for arcs in degrees
    pub clockwise: Option<bool>,  // Arc direction
    pub svg_path: Option<String>, // SVG path data for complex shapes
    pub points: Option<Vec<Vec<f64>>>, // Array of [x, y, z, bulge] points for polylines/polygons
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct LuaExecutionResult {
    pub success: bool,
    pub output: String,
    pub error: Option<String>,
    pub draw_commands: Vec<DrawCommand>,
    pub execution_time_ms: u64,
    pub debug_state: Option<DebugState>,
    pub makerjs_json: Option<String>, // JSON output from makerjs_engine
    pub makerjs_svg: Option<String>,  // SVG output from makerjs_engine
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct DebugState {
    pub is_debugging: bool,
    pub is_paused: bool,
    pub current_line: u32,
    pub current_file: String,
    pub call_stack: Vec<String>,
    pub variables: std::collections::HashMap<String, String>,
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct BreakpointInfo {
    pub file_path: String,
    pub line_number: u32,
    pub enabled: bool,
    pub condition: Option<String>,
}



#[derive(Debug, Clone)]
pub struct GraphicsState {
    pub x: f64,
    pub y: f64,
    pub angle: f64,
    pub pen_down: bool,
    pub pen_color: String,
    pub pen_size: f64,
}

impl Default for GraphicsState {
    fn default() -> Self {
        Self {
            x: 0.0,
            y: 0.0,
            angle: 0.0,
            pen_down: true,
            pen_color: "black".to_string(),
            pen_size: 1.0,
        }
    }
}

#[derive(Debug, Clone)]
pub struct AdekoState {
    #[allow(dead_code)]
    pub current_face: String,
    #[allow(dead_code)]
    pub current_layer: String,
    #[allow(dead_code)]
    pub current_thickness: f64,
    #[allow(dead_code)]
    pub show_points_enabled: bool,
    #[allow(dead_code)]
    pub listing_enabled: bool,
    #[allow(dead_code)]
    pub part_width: f64,
    #[allow(dead_code)]
    pub part_height: f64,
}

impl Default for AdekoState {
    fn default() -> Self {
        Self {
            current_face: "top".to_string(),
            current_layer: "default".to_string(),
            current_thickness: 0.0,
            show_points_enabled: false,
            listing_enabled: false,
            part_width: 0.0,
            part_height: 0.0,
        }
    }
}

pub struct NativeLuaEngine {
    lua: Lua,
    output_buffer: Arc<Mutex<Vec<String>>>,
    graphics_state: Arc<Mutex<GraphicsState>>,
    #[allow(dead_code)]
    adeko_state: Arc<Mutex<AdekoState>>,
    draw_commands: Arc<Mutex<Vec<DrawCommand>>>,
    lua_library_path: Option<String>,
    debug_state: Arc<Mutex<Option<DebugState>>>,
    pub breakpoints: Arc<Mutex<Vec<BreakpointInfo>>>,
    makerjs_json: Arc<Mutex<Option<String>>>, // Store JSON output from makerjs_engine
    makerjs_svg: Arc<Mutex<Option<String>>>,  // Store SVG output from makerjs_engine
}

impl NativeLuaEngine {
    #[allow(dead_code)]
    pub fn new() -> LuaResult<Self> {
        Self::new_with_library_path(None)
    }

    pub fn new_with_library_path(lua_library_path: Option<String>) -> LuaResult<Self> {
        println!("=== CREATING NEW LUA ENGINE ===");
        println!("Library path: {:?}", lua_library_path);

        // Create Lua with standard libraries
        let lua = Lua::new();
        let output_buffer = Arc::new(Mutex::new(Vec::new()));
        let graphics_state = Arc::new(Mutex::new(GraphicsState::default()));
        let adeko_state = Arc::new(Mutex::new(AdekoState::default()));
        let draw_commands = Arc::new(Mutex::new(Vec::new()));

        let debug_state = Arc::new(Mutex::new(None));
        let breakpoints = Arc::new(Mutex::new(Vec::new()));
        let makerjs_json = Arc::new(Mutex::new(None));
        let makerjs_svg = Arc::new(Mutex::new(None));

        let engine = Self {
            lua,
            output_buffer,
            graphics_state,
            adeko_state,
            draw_commands,
            lua_library_path,
            debug_state,
            breakpoints,
            makerjs_json,
            makerjs_svg,
        };

        println!("=== SETTING UP LUA ENVIRONMENT ===");
        engine.setup_lua_environment()?;
        println!("=== FINISHED SETTING UP LUA ENVIRONMENT ===");

        Ok(engine)
    }

    fn setup_lua_environment(&self) -> LuaResult<()> {
        let globals = self.lua.globals();

        // Setup package path for module loading
        self.setup_package_path(&globals)?;

        // Setup custom require function for modules
        self.setup_custom_require(&globals)?;

        // Setup custom print function with JSON and SVG capture
        let output_buffer = Arc::clone(&self.output_buffer);
        let makerjs_json_clone = Arc::clone(&self.makerjs_json);
        let makerjs_svg_clone = Arc::clone(&self.makerjs_svg);
        let print_fn = self.lua.create_function(move |_, args: mlua::Variadic<Value>| {
            let mut output = output_buffer.lock().unwrap();
            let line = args
                .iter()
                .map(|v| match v {
                    Value::String(s) => {
                        let s_str = s.to_str().unwrap_or("");

                        // Check if this looks like JSON from makerjs_engine
                        if s_str.contains("\"models\"") && s_str.contains("\"paths\"") {
                            let mut json_storage = makerjs_json_clone.lock().unwrap();
                            *json_storage = Some(s_str.to_string());
                            println!("📊 Auto-captured makerjs_engine JSON from print()");
                            println!("📊 JSON content preview: {}", &s_str[..std::cmp::min(500, s_str.len())]);
                        }

                        // Check if this looks like SVG from makerjs_engine
                        if s_str.starts_with("<?xml") && s_str.contains("<svg") && s_str.contains("</svg>") {
                            let mut svg_storage = makerjs_svg_clone.lock().unwrap();
                            *svg_storage = Some(s_str.to_string());
                            println!("🎨 Auto-captured makerjs_engine SVG from print()");
                            println!("🎨 SVG content preview: {}", &s_str[..std::cmp::min(300, s_str.len())]);
                        }

                        s_str.to_string()
                    },
                    Value::Number(n) => n.to_string(),
                    Value::Integer(i) => i.to_string(),
                    Value::Boolean(b) => b.to_string(),
                    Value::Nil => "nil".to_string(),
                    _ => format!("{:?}", v),
                })
                .collect::<Vec<_>>()
                .join("\t");
            output.push(line);
            Ok(())
        })?;
        globals.set("print", print_fn)?;

        // Setup custom printError function
        let output_buffer_error = Arc::clone(&self.output_buffer);
        let print_error_fn = self.lua.create_function(move |_, args: mlua::Variadic<Value>| {
            let mut output = output_buffer_error.lock().unwrap();
            let line = args
                .iter()
                .map(|v| match v {
                    Value::String(s) => s.to_str().unwrap_or("").to_string(),
                    Value::Number(n) => n.to_string(),
                    Value::Integer(i) => i.to_string(),
                    Value::Boolean(b) => b.to_string(),
                    Value::Nil => "nil".to_string(),
                    _ => format!("{:?}", v),
                })
                .collect::<Vec<_>>()
                .join("\t");
            // Prefix error messages with "ERROR: " to distinguish them
            output.push(format!("ERROR: {}", line));
            Ok(())
        })?;
        globals.set("printError", print_error_fn)?;

        // Setup custom printErrorUrgent function
        let output_buffer_urgent = Arc::clone(&self.output_buffer);
        let print_error_urgent_fn = self.lua.create_function(move |_, args: mlua::Variadic<Value>| {
            let mut output = output_buffer_urgent.lock().unwrap();
            let line = args
                .iter()
                .map(|v| match v {
                    Value::String(s) => s.to_str().unwrap_or("").to_string(),
                    Value::Number(n) => n.to_string(),
                    Value::Integer(i) => i.to_string(),
                    Value::Boolean(b) => b.to_string(),
                    Value::Nil => "nil".to_string(),
                    _ => format!("{:?}", v),
                })
                .collect::<Vec<_>>()
                .join("\t");
            // Prefix urgent error messages with "URGENT ERROR: " to distinguish them
            output.push(format!("URGENT ERROR: {}", line));
            Ok(())
        })?;
        globals.set("printErrorUrgent", print_error_urgent_fn)?;

        // Load bundled Lua libraries
        self.load_bundled_lua_libraries(&globals)?;

        Ok(())
    }

    fn load_bundled_lua_libraries(&self, globals: &Table) -> LuaResult<()> {
        println!("=== LOADING BUNDLED LUA LIBRARIES ===");

        // Load makerjs_engine.lua first as it's required by ADekoLib
        println!("Loading bundled makerjs_engine.lua ({} bytes)", MAKERJS_ENGINE_LUA.len());
        let makerjs_engine_table: Value = self.lua.load(MAKERJS_ENGINE_LUA).call(())?;
        globals.set("makerjs_engine", makerjs_engine_table)?;
        println!("✓ makerjs_engine.lua loaded successfully");

        // Load ADekoLib.lua as a module and set it globally
        println!("Loading bundled ADekoLib.lua ({} bytes)", ADEKO_LIB_LUA.len());

        // Execute the module and capture the returned ADekoLib table
        let adeko_lib_table: Value = self.lua.load(ADEKO_LIB_LUA).call(())?;
        globals.set("ADekoLib", adeko_lib_table)?;

        println!("✓ ADekoLib.lua loaded successfully (start() will be called before script execution)");

        // Setup built-in graphics functions
        self.setup_graphics_functions(&globals)?;

        // Don't load ADekoDebugMode.lua here - it will be loaded when required
        // because it immediately tries to execute modelMain() which doesn't exist yet
        println!("ADekoDebugMode.lua will be loaded when required by user script");

        // Setup debug mode variables
        self.setup_debug_variables(&globals)?;

        // Mark modules as loaded in package.loaded
        let package: Table = globals.get("package")?;
        let loaded: Table = package.get("loaded")?;

        let adeko_lib = globals.get::<_, Value>("ADekoLib")?;
        let makerjs_engine = globals.get::<_, Value>("makerjs_engine")?;
        loaded.set("ADekoLib", adeko_lib)?;
        loaded.set("makerjs_engine", makerjs_engine)?;
        // Don't mark ADekoDebugMode as loaded yet - it will be loaded when required

        println!("✓ All bundled Lua libraries loaded successfully");
        Ok(())
    }

    fn setup_package_path(&self, globals: &Table) -> LuaResult<()> {
        // Get the package table
        let package: Table = globals.get("package")?;

        // Get current path
        let current_path: String = package.get("path").unwrap_or_else(|_| "?.lua;?/init.lua".to_string());

        // Add lua library path if available
        let new_path = if let Some(ref lib_path) = self.lua_library_path {
            format!("{};{}/?.lua;{}/?/init.lua", current_path, lib_path, lib_path)
        } else {
            current_path
        };

        package.set("path", new_path)?;
        Ok(())
    }

    fn setup_custom_require(&self, globals: &Table) -> LuaResult<()> {
        use std::fs;
        use std::path::Path;

        let lua_library_path = self.lua_library_path.clone();

        let custom_require = self.lua.create_function(move |lua, module_name: String| {
            println!("Custom require called for module: {}", module_name);
            let package: Table = lua.globals().get("package")?;
            let loaded: Table = package.get("loaded")?;

            // Check if module is already loaded
            if let Ok(module) = loaded.get::<_, Value>(module_name.clone()) {
                if !matches!(module, Value::Nil) {
                    println!("Module {} already loaded", module_name);
                    return Ok(module);
                }
            }

            // Special handling for bundled modules
            match module_name.as_str() {
                "ADekoLib" => {
                    println!("ADekoLib module requested - checking if already loaded globally");
                    let adeko_lib = lua.globals().get::<_, Value>("ADekoLib")?;
                    if !matches!(adeko_lib, Value::Nil) {
                        loaded.set(module_name.clone(), adeko_lib.clone())?;
                        println!("✓ ADekoLib module found globally and returned");
                        return Ok(adeko_lib);
                    }

                    // If not found, this shouldn't happen since we load it at startup
                    println!("⚠ Warning: ADekoLib not found globally, this shouldn't happen");
                    return Err(mlua::Error::RuntimeError("ADekoLib should be loaded globally".to_string()));
                }
                "makerjs_engine" => {
                    println!("makerjs_engine module requested - checking if already loaded globally");
                    let makerjs_engine = lua.globals().get::<_, Value>("makerjs_engine")?;
                    if !matches!(makerjs_engine, Value::Nil) {
                        loaded.set(module_name.clone(), makerjs_engine.clone())?;
                        println!("✓ makerjs_engine module found globally and returned");
                        return Ok(makerjs_engine);
                    }

                    // If not found, this shouldn't happen since we load it at startup
                    println!("⚠ Warning: makerjs_engine not found globally, this shouldn't happen");
                    return Err(mlua::Error::RuntimeError("makerjs_engine should be loaded globally".to_string()));
                }

                "ADekoDebugMode" => {
                    println!("ADekoDebugMode module requested - loading bundled version");
                    // Load ADekoDebugMode.lua now (when it's actually needed)
                    lua.load(ADEKO_DEBUG_MODE_LUA).exec()?;
                    let result = Value::Boolean(true);
                    loaded.set(module_name.clone(), result.clone())?;
                    println!("✓ ADekoDebugMode module loaded successfully");
                    return Ok(result);
                }
                _ => {
                    println!("Module {} not found in bundled modules", module_name);
                }
            }

            // Handle external library modules (excluding ADekoDebugMode which is handled above)
            if let Some(ref lib_path) = lua_library_path {
                let module_file = Path::new(lib_path).join(format!("{}.lua", module_name));

                if module_file.exists() {
                    if let Ok(content) = fs::read_to_string(&module_file) {
                        // For other modules, execute and return the result
                        let result = lua.load(&content).call::<_, Value>(())?;
                        loaded.set(module_name.clone(), result.clone())?;
                        return Ok(result);
                    }
                }
            }

            // If we can't find the module, return an error
            Err(mlua::Error::RuntimeError(format!("module '{}' not found", module_name)))
        })?;

        globals.set("require", custom_require)?;
        Ok(())
    }



    fn setup_graphics_functions(&self, globals: &Table) -> LuaResult<()> {
        let graphics_state = Arc::clone(&self.graphics_state);
        let output_buffer = Arc::clone(&self.output_buffer);
        let draw_commands = Arc::clone(&self.draw_commands);
        let adeko_state = Arc::clone(&self.adeko_state);

        // move function
        let graphics_state_clone = Arc::clone(&graphics_state);
        let output_buffer_clone = Arc::clone(&output_buffer);
        let draw_commands_clone = Arc::clone(&draw_commands);
        let _adeko_state_clone = Arc::clone(&adeko_state);
        let lua_ref = &self.lua;
        let move_fn = lua_ref.create_function(move |lua, distance: f64| {
            let mut state = graphics_state_clone.lock().unwrap();
            let mut output = output_buffer_clone.lock().unwrap();
            let mut commands = draw_commands_clone.lock().unwrap();

            println!("Move called with distance: {}", distance);

            let new_x = state.x + distance * state.angle.to_radians().cos();
            let new_y = state.y + distance * state.angle.to_radians().sin();

            if state.pen_down {
                output.push(format!("Drawing line from ({:.2}, {:.2}) to ({:.2}, {:.2})",
                    state.x, state.y, new_x, new_y));

                // Get current layer, thickness, and face from global variables
                let globals = lua.globals();
                let current_layer: String = globals.get("currentLayerName").unwrap_or_else(|_| "default".to_string());
                let current_thickness: Option<f64> = globals.get("currentThickness").ok();
                let current_face = state.current_face.clone();

                // Get layer-specific color
                let layer_color = get_layer_color(&current_layer);

                commands.push(DrawCommand {
                    command_type: "line".to_string(),
                    x1: state.x,
                    y1: state.y,
                    x2: new_x,
                    y2: new_y,
                    z1: None, // 2D line, no Z coordinate
                    z2: None, // 2D line, no Z coordinate
                    radius: 0.0,
                    color: layer_color,
                    size: state.pen_size,
                    text: String::new(),
                    layer_name: current_layer,
                    thickness: current_thickness,
                    face: Some(current_face),
                    start_angle: None,
                    end_angle: None,
                    clockwise: None,
                    svg_path: None,
                    points: None,
                });
                println!("Added line draw command, total commands: {}", commands.len());
            } else {
                output.push(format!("Moving from ({:.2}, {:.2}) to ({:.2}, {:.2})",
                    state.x, state.y, new_x, new_y));
            }

            state.x = new_x;
            state.y = new_y;
            Ok(())
        })?;
        globals.set("move", move_fn)?;

        // turn function
        let graphics_state_clone = Arc::clone(&graphics_state);
        let output_buffer_clone = Arc::clone(&output_buffer);
        let turn_fn = self.lua.create_function(move |_, degrees: f64| {
            let mut state = graphics_state_clone.lock().unwrap();
            let mut output = output_buffer_clone.lock().unwrap();
            
            state.angle += degrees;
            output.push(format!("Turned {:.2} degrees, now facing {:.2} degrees", degrees, state.angle));
            Ok(())
        })?;
        globals.set("turn", turn_fn)?;

        // pndn function (pen down)
        let graphics_state_clone = Arc::clone(&graphics_state);
        let output_buffer_clone = Arc::clone(&output_buffer);
        let pndn_fn = self.lua.create_function(move |_, ()| {
            let mut state = graphics_state_clone.lock().unwrap();
            let mut output = output_buffer_clone.lock().unwrap();

            state.pen_down = true;
            println!("Pen down called - pen_down state: {}", state.pen_down);
            output.push("Pen down".to_string());
            Ok(())
        })?;
        globals.set("pndn", pndn_fn)?;

        // pnup function (pen up)
        let graphics_state_clone = Arc::clone(&graphics_state);
        let output_buffer_clone = Arc::clone(&output_buffer);
        let pnup_fn = self.lua.create_function(move |_, ()| {
            let mut state = graphics_state_clone.lock().unwrap();
            let mut output = output_buffer_clone.lock().unwrap();
            
            state.pen_down = false;
            output.push("Pen up".to_string());
            Ok(())
        })?;
        globals.set("pnup", pnup_fn)?;

        // posn function (set position)
        let graphics_state_clone = Arc::clone(&graphics_state);
        let output_buffer_clone = Arc::clone(&output_buffer);
        let posn_fn = self.lua.create_function(move |_, (x, y): (f64, f64)| {
            let mut state = graphics_state_clone.lock().unwrap();
            let mut output = output_buffer_clone.lock().unwrap();
            
            state.x = x;
            state.y = y;
            output.push(format!("Position set to ({:.2}, {:.2})", x, y));
            Ok(())
        })?;
        globals.set("posn", posn_fn)?;

        // zero function (reset position)
        let graphics_state_clone = Arc::clone(&graphics_state);
        let output_buffer_clone = Arc::clone(&output_buffer);
        let zero_fn = self.lua.create_function(move |_, args: mlua::Variadic<f64>| {
            let mut state = graphics_state_clone.lock().unwrap();
            let mut output = output_buffer_clone.lock().unwrap();
            
            let x = args.get(0).copied().unwrap_or(0.0);
            let y = args.get(1).copied().unwrap_or(0.0);
            
            state.x = x;
            state.y = y;
            output.push(format!("Zero position set to ({:.2}, {:.2})", x, y));
            Ok(())
        })?;
        globals.set("zero", zero_fn)?;

        // pncl function (pen color)
        let graphics_state_clone = Arc::clone(&graphics_state);
        let output_buffer_clone = Arc::clone(&output_buffer);
        let pncl_fn = self.lua.create_function(move |_, color: String| {
            let mut state = graphics_state_clone.lock().unwrap();
            let mut output = output_buffer_clone.lock().unwrap();

            state.pen_color = color.clone();
            output.push(format!("Pen color set to: {}", color));
            Ok(())
        })?;
        globals.set("pncl", pncl_fn)?;

        // pnsz function (pen size)
        let graphics_state_clone = Arc::clone(&graphics_state);
        let output_buffer_clone = Arc::clone(&output_buffer);
        let pnsz_fn = self.lua.create_function(move |_, size: f64| {
            let mut state = graphics_state_clone.lock().unwrap();
            let mut output = output_buffer_clone.lock().unwrap();

            state.pen_size = size;
            output.push(format!("Pen size set to: {}", size));
            Ok(())
        })?;
        globals.set("pnsz", pnsz_fn)?;

        // wipe function (clear screen)
        let graphics_state_clone = Arc::clone(&graphics_state);
        let output_buffer_clone = Arc::clone(&output_buffer);
        let wipe_fn = self.lua.create_function(move |_, ()| {
            let mut state = graphics_state_clone.lock().unwrap();
            let mut output = output_buffer_clone.lock().unwrap();

            *state = GraphicsState::default();
            output.push("Screen cleared".to_string());
            Ok(())
        })?;
        globals.set("wipe", wipe_fn)?;

        // text function - signature: text(text, angle, dx, dy)
        let output_buffer_clone = Arc::clone(&output_buffer);
        let draw_commands_clone = Arc::clone(&draw_commands);
        let graphics_state_clone = Arc::clone(&graphics_state);
        let _adeko_state_clone = Arc::clone(&adeko_state);
        let lua_ref = &self.lua;
        let text_fn = lua_ref.create_function(move |lua, args: mlua::Variadic<Value>| {
            let mut output = output_buffer_clone.lock().unwrap();
            let mut commands = draw_commands_clone.lock().unwrap();
            let state = graphics_state_clone.lock().unwrap();

            // Parse arguments: text(text, angle, dx, dy)
            let text_content = args.get(0).map(|v| match v {
                Value::String(s) => s.to_str().unwrap_or("").to_string(),
                Value::Number(n) => n.to_string(),
                Value::Integer(i) => i.to_string(),
                Value::Boolean(b) => b.to_string(),
                Value::Nil => "nil".to_string(),
                _ => format!("{:?}", v),
            }).unwrap_or_default();

            let _angle = args.get(1).and_then(|v| v.as_number()).unwrap_or(0.0);
            let dx = args.get(2).and_then(|v| v.as_number()).unwrap_or(0.0);
            let dy = args.get(3).and_then(|v| v.as_number()).unwrap_or(0.0);

            // Calculate final position (turtle position + offset)
            let x = state.x + dx;
            let y = state.y + dy;

            output.push(format!("Text at ({:.2}, {:.2}): {}", x, y, text_content));

            // Get current layer and face from global variable
            let globals = lua.globals();
            let current_layer: String = globals.get("currentLayerName").unwrap_or_else(|_| "default".to_string());
            let current_face = state.current_face.clone();

            // Get layer-specific color
            let layer_color = get_layer_color(&current_layer);

            // Add draw command for text
            commands.push(DrawCommand {
                command_type: "text".to_string(),
                x1: x,
                y1: y,
                x2: 0.0,
                y2: 0.0,
                z1: None, // 2D text, no Z coordinate
                z2: None, // 2D text, no Z coordinate
                radius: 0.0,
                color: layer_color,
                size: state.pen_size,
                text: text_content,
                layer_name: current_layer,
                thickness: None, // Text doesn't have thickness
                face: Some(current_face),
                start_angle: None,
                end_angle: None,
                clockwise: None,
                svg_path: None,
                points: None,
            });

            Ok(())
        })?;
        globals.set("text", text_fn)?;

        // open function (open graphics window)
        let output_buffer_clone = Arc::clone(&output_buffer);
        let open_fn = self.lua.create_function(move |_, title: Option<String>| {
            let mut output = output_buffer_clone.lock().unwrap();
            let window_title = title.unwrap_or_else(|| "Graphics Window".to_string());
            output.push(format!("Graphics window opened: {}", window_title));
            Ok(())
        })?;
        globals.set("open", open_fn)?;

        // crcl function (circle/arc)
        let output_buffer_clone = Arc::clone(&output_buffer);
        let draw_commands_clone = Arc::clone(&draw_commands);
        let graphics_state_clone = Arc::clone(&graphics_state);
        let _adeko_state_clone = Arc::clone(&adeko_state);
        let lua_ref = &self.lua;
        let crcl_fn = lua_ref.create_function(move |lua, args: mlua::Variadic<mlua::Value>| {
            let mut output = output_buffer_clone.lock().unwrap();
            let mut commands = draw_commands_clone.lock().unwrap();
            let state = graphics_state_clone.lock().unwrap();

            // Parse arguments: x, y, radius, [unused], [start_angle], [end_angle]
            let x = args.get(0).and_then(|v| v.as_number()).unwrap_or(0.0);
            let y = args.get(1).and_then(|v| v.as_number()).unwrap_or(0.0);
            let radius = args.get(2).and_then(|v| v.as_number()).unwrap_or(0.0);
            // args[3] is often nil/unused in ADekoLib calls
            let start_angle = args.get(4).and_then(|v| v.as_number());
            let end_angle = args.get(5).and_then(|v| v.as_number());

            let is_arc = start_angle.is_some() && end_angle.is_some();

            if is_arc {
                println!("crcl called as arc: x: {}, y: {}, radius: {}, start: {:?}, end: {:?}",
                    x, y, radius, start_angle, end_angle);
                output.push(format!("Arc at ({:.2}, {:.2}) with radius {:.2}, angles {:.1}° to {:.1}°",
                    x, y, radius, start_angle.unwrap_or(0.0), end_angle.unwrap_or(0.0)));
            } else {
                println!("crcl called as circle: x: {}, y: {}, radius: {}", x, y, radius);
                output.push(format!("Circle at ({:.2}, {:.2}) with radius {:.2}", x, y, radius));
            }

            // Get current layer, thickness, and face from global variables
            let globals = lua.globals();
            let current_layer: String = globals.get("currentLayerName").unwrap_or_else(|_| "default".to_string());
            let current_thickness: Option<f64> = globals.get("currentThickness").ok();
            let current_face = state.current_face.clone();

            // Get layer-specific color
            let layer_color = get_layer_color(&current_layer);

            // Add draw command for circle or arc
            commands.push(DrawCommand {
                command_type: if is_arc { "arc".to_string() } else { "circle".to_string() },
                x1: x,
                y1: y,
                x2: 0.0,
                y2: 0.0,
                z1: None, // 2D circle/arc, no Z coordinate
                z2: None, // 2D circle/arc, no Z coordinate
                radius,
                color: layer_color,
                size: state.pen_size,
                text: String::new(),
                layer_name: current_layer,
                thickness: current_thickness,
                face: Some(current_face),
                start_angle,
                end_angle,
                clockwise: None, // TODO: Add clockwise parameter to crcl function
                svg_path: None,
                points: None,
            });
            println!("Added {} draw command, total commands: {}",
                if is_arc { "arc" } else { "circle" }, commands.len());
            Ok(())
        })?;
        globals.set("crcl", crcl_fn)?;

        // line function
        let output_buffer_clone = Arc::clone(&output_buffer);
        let draw_commands_clone = Arc::clone(&draw_commands);
        let graphics_state_clone = Arc::clone(&graphics_state);
        let _adeko_state_clone = Arc::clone(&adeko_state);
        let lua_ref = &self.lua;
        let line_fn = lua_ref.create_function(move |lua, (x1, y1, x2, y2): (f64, f64, f64, f64)| {
            let mut output = output_buffer_clone.lock().unwrap();
            let mut commands = draw_commands_clone.lock().unwrap();
            let state = graphics_state_clone.lock().unwrap();

            output.push(format!("Line from ({:.2}, {:.2}) to ({:.2}, {:.2})", x1, y1, x2, y2));

            // Get current layer, thickness, and face from global variables
            let globals = lua.globals();
            let current_layer: String = globals.get("currentLayerName").unwrap_or_else(|_| "default".to_string());
            let current_thickness: Option<f64> = globals.get("currentThickness").ok();
            let current_face = state.current_face.clone();

            // Get layer-specific color
            let layer_color = get_layer_color(&current_layer);

            // Add draw command for line
            commands.push(DrawCommand {
                command_type: "line".to_string(),
                x1,
                y1,
                x2,
                y2,
                z1: None, // 2D line, no Z coordinate
                z2: None, // 2D line, no Z coordinate
                radius: 0.0,
                color: layer_color,
                size: state.pen_size,
                text: String::new(),
                layer_name: current_layer,
                thickness: current_thickness,
                face: Some(current_face),
                start_angle: None,
                end_angle: None,
                clockwise: None,
                svg_path: None,
                points: None,
            });
            Ok(())
        })?;
        globals.set("line", line_fn)?;

        // rect function (rectangle)
        let output_buffer_clone = Arc::clone(&output_buffer);
        let draw_commands_clone = Arc::clone(&draw_commands);
        let graphics_state_clone = Arc::clone(&graphics_state);
        let _adeko_state_clone = Arc::clone(&adeko_state);
        let lua_ref = &self.lua;
        let rect_fn = lua_ref.create_function(move |lua, args: mlua::Variadic<f64>| {
            let mut output = output_buffer_clone.lock().unwrap();
            let mut commands = draw_commands_clone.lock().unwrap();
            let state = graphics_state_clone.lock().unwrap();

            let x = args.get(0).copied().unwrap_or(0.0);
            let y = args.get(1).copied().unwrap_or(0.0);
            let width = args.get(2).copied().unwrap_or(0.0);
            let height = args.get(3).copied().unwrap_or(0.0);
            let corner_radius = args.get(4).copied().unwrap_or(0.0);

            output.push(format!("Rectangle at ({:.2}, {:.2}) size {:.2}x{:.2} radius {:.2}",
                x, y, width, height, corner_radius));

            // Get current layer, thickness, and face from global variables
            let globals = lua.globals();
            let current_layer: String = globals.get("currentLayerName").unwrap_or_else(|_| "default".to_string());
            let current_thickness: Option<f64> = globals.get("currentThickness").ok();
            let current_face = state.current_face.clone();

            // Get layer-specific color
            let layer_color = get_layer_color(&current_layer);

            // Add draw command for rectangle
            commands.push(DrawCommand {
                command_type: "rectangle".to_string(),
                x1: x,
                y1: y,
                x2: x + width,
                y2: y + height,
                z1: None, // 2D rectangle, no Z coordinate
                z2: None, // 2D rectangle, no Z coordinate
                radius: corner_radius,
                color: layer_color,
                size: state.pen_size,
                text: String::new(),
                layer_name: current_layer,
                thickness: current_thickness,
                face: Some(current_face),
                start_angle: None,
                end_angle: None,
                clockwise: None,
                svg_path: None,
                points: None,
            });
            Ok(())
        })?;
        globals.set("rect", rect_fn)?;

        // colr function (color)
        let colr_fn = self.lua.create_function(move |_, (r, g, b): (i32, i32, i32)| {
            Ok(format!("rgb({},{},{})", r, g, b))
        })?;
        globals.set("colr", colr_fn)?;

        // wait function
        let output_buffer_clone = Arc::clone(&output_buffer);
        let wait_fn = self.lua.create_function(move |_, ()| {
            let mut output = output_buffer_clone.lock().unwrap();
            output.push("Waiting for user input...".to_string());
            Ok(())
        })?;
        globals.set("wait", wait_fn)?;

        // pixl function (draw pixel)
        let output_buffer_clone = Arc::clone(&output_buffer);
        let draw_commands_clone = Arc::clone(&draw_commands);
        let graphics_state_clone = Arc::clone(&graphics_state);
        let _adeko_state_clone = Arc::clone(&adeko_state);
        let lua_ref = &self.lua;
        let pixl_fn = lua_ref.create_function(move |lua, (x, y): (f64, f64)| {
            let mut output = output_buffer_clone.lock().unwrap();
            let mut commands = draw_commands_clone.lock().unwrap();
            let state = graphics_state_clone.lock().unwrap();

            output.push(format!("Pixel at ({:.2}, {:.2})", x, y));

            // Get current layer, thickness, and face from global variables
            let globals = lua.globals();
            let current_layer: String = globals.get("currentLayerName").unwrap_or_else(|_| "default".to_string());
            let current_thickness: Option<f64> = globals.get("currentThickness").ok();
            let current_face = state.current_face.clone();

            // Get layer-specific color
            let layer_color = get_layer_color(&current_layer);

            // Add draw command for a small circle to represent a pixel
            commands.push(DrawCommand {
                command_type: "circle".to_string(),
                x1: state.x + x,
                y1: state.y + y,
                x2: 0.0,
                y2: 0.0,
                z1: None, // 2D pixel, no Z coordinate
                z2: None, // 2D pixel, no Z coordinate
                radius: 1.0, // Small radius for pixel
                color: layer_color,
                size: state.pen_size,
                text: String::new(),
                layer_name: current_layer,
                thickness: current_thickness,
                face: Some(current_face),
                start_angle: None,
                end_angle: None,
                clockwise: None,
                svg_path: None,
                points: None,
            });
            Ok(())
        })?;
        globals.set("pixl", pixl_fn)?;

        // rand function (random number)
        let rand_fn = self.lua.create_function(move |_, max: Option<f64>| {
            use std::collections::hash_map::DefaultHasher;
            use std::hash::{Hash, Hasher};
            use std::time::{SystemTime, UNIX_EPOCH};

            let mut hasher = DefaultHasher::new();
            SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_nanos().hash(&mut hasher);
            let hash = hasher.finish();

            let max_val = max.unwrap_or(1.0);
            let random_val = (hash as f64 / u64::MAX as f64) * max_val;
            Ok(random_val)
        })?;
        globals.set("rand", rand_fn)?;

        // ranc function (random color)
        let ranc_fn = self.lua.create_function(move |_, ()| {
            use std::collections::hash_map::DefaultHasher;
            use std::hash::{Hash, Hasher};
            use std::time::{SystemTime, UNIX_EPOCH};

            let mut hasher = DefaultHasher::new();
            SystemTime::now().duration_since(UNIX_EPOCH).unwrap().as_nanos().hash(&mut hasher);
            let hash = hasher.finish();

            let colors = ["red", "green", "blue", "yellow", "purple", "orange", "pink", "cyan"];
            let index = (hash as usize) % colors.len();
            Ok(colors[index].to_string())
        })?;
        globals.set("ranc", ranc_fn)?;

        Ok(())
    }

    #[allow(dead_code)]
    fn setup_adeko_functions(&self, globals: &Table) -> LuaResult<()> {
        // Create ADekoLib table
        let adeko_lib = self.lua.create_table()?;
        
        let adeko_state = Arc::clone(&self.adeko_state);
        let output_buffer = Arc::clone(&self.output_buffer);

        // setFace function
        let adeko_state_clone = Arc::clone(&adeko_state);
        let output_buffer_clone = Arc::clone(&output_buffer);
        let set_face_fn = self.lua.create_function(move |_, face: String| {
            let mut state = adeko_state_clone.lock().unwrap();
            let mut output = output_buffer_clone.lock().unwrap();
            
            state.current_face = face.clone();
            output.push(format!("Set face to: {}", face));
            Ok(())
        })?;
        adeko_lib.set("setFace", set_face_fn)?;

        // setLayer function
        let adeko_state_clone = Arc::clone(&adeko_state);
        let output_buffer_clone = Arc::clone(&output_buffer);
        let lua_ref = &self.lua;
        let set_layer_fn = lua_ref.create_function(move |lua, layer: String| {
            let mut state = adeko_state_clone.lock().unwrap();
            let mut output = output_buffer_clone.lock().unwrap();

            state.current_layer = layer.clone();
            output.push(format!("Set layer to: {}", layer));

            // Also set the global currentLayerName variable for ADekoLib compatibility
            let globals = lua.globals();
            globals.set("currentLayerName", layer.clone())?;

            Ok(())
        })?;
        adeko_lib.set("setLayer", set_layer_fn)?;

        // makePart function
        let adeko_state_clone = Arc::clone(&adeko_state);
        let output_buffer_clone = Arc::clone(&output_buffer);
        let make_part_fn = self.lua.create_function(move |_, (width, height): (f64, f64)| {
            let mut state = adeko_state_clone.lock().unwrap();
            let mut output = output_buffer_clone.lock().unwrap();
            
            state.part_width = width;
            state.part_height = height;
            output.push(format!("Created part: {:.2} x {:.2}", width, height));
            Ok(())
        })?;
        adeko_lib.set("makePart", make_part_fn)?;

        // setThickness function
        let adeko_state_clone = Arc::clone(&adeko_state);
        let output_buffer_clone = Arc::clone(&output_buffer);
        let lua_ref = &self.lua;
        let set_thickness_fn = lua_ref.create_function(move |lua, thickness: f64| {
            let mut state = adeko_state_clone.lock().unwrap();
            let mut output = output_buffer_clone.lock().unwrap();

            state.current_thickness = thickness;
            output.push(format!("Set thickness to: {}", thickness));

            // Also set the global currentThickness variable for drawing commands
            let globals = lua.globals();
            globals.set("currentThickness", thickness)?;

            Ok(())
        })?;
        adeko_lib.set("setThickness", set_thickness_fn)?;

        // point function
        let output_buffer_clone = Arc::clone(&output_buffer);
        let point_fn = self.lua.create_function(move |_, (x, y): (f64, f64)| {
            let mut output = output_buffer_clone.lock().unwrap();
            output.push(format!("Point at ({:.2}, {:.2})", x, y));
            Ok(())
        })?;
        adeko_lib.set("point", point_fn)?;

        // pointSize function
        let output_buffer_clone = Arc::clone(&output_buffer);
        let point_size_fn = self.lua.create_function(move |_, size: f64| {
            let mut output = output_buffer_clone.lock().unwrap();
            output.push(format!("Point size set to: {}", size));
            Ok(())
        })?;
        adeko_lib.set("pointSize", point_size_fn)?;

        // circle function
        let output_buffer_clone = Arc::clone(&output_buffer);
        let circle_fn = self.lua.create_function(move |_, args: mlua::Variadic<Value>| {
            let mut output = output_buffer_clone.lock().unwrap();

            if args.len() >= 2 {
                // circle({x, y}, radius) format
                if let (Some(center), Some(radius)) = (args.get(0), args.get(1)) {
                    if let Some(center_table) = center.as_table() {
                        let x: f64 = center_table.get(1).unwrap_or(0.0);
                        let y: f64 = center_table.get(2).unwrap_or(0.0);
                        let r: f64 = radius.as_number().unwrap_or(0.0);
                        output.push(format!("Circle at ({:.2}, {:.2}) with radius {:.2}", x, y, r));
                    }
                }
            }
            Ok(())
        })?;
        adeko_lib.set("circle", circle_fn)?;

        // rectangle function
        let output_buffer_clone = Arc::clone(&output_buffer);
        let rectangle_fn = self.lua.create_function(move |_, (p1, p2): (Table, Table)| {
            let mut output = output_buffer_clone.lock().unwrap();

            let x1: f64 = p1.get(1).unwrap_or(0.0);
            let y1: f64 = p1.get(2).unwrap_or(0.0);
            let x2: f64 = p2.get(1).unwrap_or(0.0);
            let y2: f64 = p2.get(2).unwrap_or(0.0);

            output.push(format!("Rectangle from ({:.2}, {:.2}) to ({:.2}, {:.2})", x1, y1, x2, y2));
            Ok(())
        })?;
        adeko_lib.set("rectangle", rectangle_fn)?;

        // line function
        let output_buffer_clone = Arc::clone(&output_buffer);
        let adeko_line_fn = self.lua.create_function(move |_, args: mlua::Variadic<Value>| {
            let mut output = output_buffer_clone.lock().unwrap();

            if args.len() >= 2 {
                if let (Some(p1), Some(p2)) = (args.get(0), args.get(1)) {
                    if let (Some(p1_table), Some(p2_table)) = (p1.as_table(), p2.as_table()) {
                        let x1: f64 = p1_table.get(1).unwrap_or(0.0);
                        let y1: f64 = p1_table.get(2).unwrap_or(0.0);
                        let x2: f64 = p2_table.get(1).unwrap_or(0.0);
                        let y2: f64 = p2_table.get(2).unwrap_or(0.0);

                        let bulge = args.get(2).and_then(|v| v.as_number()).unwrap_or(0.0);
                        if bulge != 0.0 {
                            output.push(format!("Arc from ({:.2}, {:.2}) to ({:.2}, {:.2}) with bulge {:.2}",
                                x1, y1, x2, y2, bulge));
                        } else {
                            output.push(format!("Line from ({:.2}, {:.2}) to ({:.2}, {:.2})", x1, y1, x2, y2));
                        }
                    }
                }
            }
            Ok(())
        })?;
        adeko_lib.set("line", adeko_line_fn)?;

        // polylineimp function
        let output_buffer_clone = Arc::clone(&output_buffer);
        let polyline_fn = self.lua.create_function(move |_, points: Table| {
            let mut output = output_buffer_clone.lock().unwrap();

            let mut point_count = 0;
            for pair in points.pairs::<i32, Table>() {
                if let Ok((_, point)) = pair {
                    let x: f64 = point.get(1).unwrap_or(0.0);
                    let y: f64 = point.get(2).unwrap_or(0.0);
                    point_count += 1;
                    output.push(format!("  Point {}: ({:.2}, {:.2})", point_count, x, y));
                }
            }
            output.push(format!("Polyline with {} points", point_count));
            Ok(())
        })?;
        adeko_lib.set("polylineimp", polyline_fn)?;

        globals.set("ADekoLib", adeko_lib)?;
        Ok(())
    }

    fn setup_debug_variables(&self, globals: &Table) -> LuaResult<()> {
        // ADekoDebugMode variables
        globals.set("X", 500.0)?;
        globals.set("Y", 700.0)?;
        globals.set("modelParameters", "")?;
        globals.set("materialThickness", 18.0)?;
        globals.set("offset", 20.0)?;
        globals.set("edge1layer", "LMM0")?;
        globals.set("edge2layer", "LMM1")?;
        globals.set("edge3layer", "LMM2")?;
        globals.set("edge4layer", "LMM3")?;
        globals.set("edge1thickness", 0.1)?;
        globals.set("edge2thickness", 0.2)?;
        globals.set("edge3thickness", 0.3)?;
        globals.set("edge4thickness", 0.4)?;
        globals.set("doesSizeIncludeEdgeThickness", "false")?;
        globals.set("currentLayerName", "LUA")?;
        globals.set("currentThickness", 0.0)?; // Initialize current thickness

        Ok(())
    }

    pub fn set_breakpoints(&self, breakpoints: Vec<BreakpointInfo>) {
        let mut bp_guard = self.breakpoints.lock().unwrap();
        *bp_guard = breakpoints;
    }

    pub fn execute_script(&self, script: &str) -> LuaExecutionResult {
        self.execute_script_with_debug(script, false)
    }

    pub fn execute_script_with_debug(&self, script: &str, enable_debugging: bool) -> LuaExecutionResult {
        let start_time = std::time::Instant::now();

        // Clear output buffer and draw commands
        self.output_buffer.lock().unwrap().clear();
        self.draw_commands.lock().unwrap().clear();

        // Initialize debug state if debugging is enabled
        if enable_debugging {
            let mut debug_state = self.debug_state.lock().unwrap();
            *debug_state = Some(DebugState {
                is_debugging: true,
                is_paused: false,
                current_line: 1,
                current_file: "script.lua".to_string(),
                call_stack: vec!["main".to_string()],
                variables: std::collections::HashMap::new(),
            });
        }

        println!("Executing Lua script (length: {})", script.len());
        println!("Script content preview: {}", &script[..std::cmp::min(200, script.len())]);

        // Setup debug hook if debugging is enabled
        if enable_debugging {
            self.setup_debug_hook();
        }

        // Inject breakpoint checks into the script if debugging is enabled
        let processed_script = if enable_debugging {
            self.inject_breakpoint_checks(script)
        } else {
            script.to_string()
        };

        // Initialize ADekoLib before script execution
        println!("Calling ADekoLib.start() to initialize the library");
        match self.lua.load("ADekoLib.start()").exec() {
            Ok(_) => println!("✓ ADekoLib.start() called successfully"),
            Err(e) => println!("⚠ Warning: ADekoLib.start() failed: {}", e),
        }

        // Execute the processed script
        match self.lua.load(&processed_script).exec() {
            Ok(_) => {
                let mut output = self.output_buffer.lock().unwrap().join("\n");
                let draw_commands = self.draw_commands.lock().unwrap().clone();
                println!("Script execution completed successfully. Output lines: {}, Draw commands: {}",
                    self.output_buffer.lock().unwrap().len(), draw_commands.len());

                // Debug: Check if modelMain function exists in global scope
                let model_main_status = match self.lua.globals().get::<_, mlua::Function>("modelMain") {
                    Ok(_) => {
                        println!("modelMain function found in global scope");
                        "modelMain function found in global scope"
                    },
                    Err(_) => {
                        println!("modelMain function NOT found in global scope");
                        "modelMain function NOT found in global scope"
                    }
                };

                // Add debug info to output so it's visible in the frontend
                if !output.is_empty() {
                    output.push_str("\n");
                }
                output.push_str(&format!("DEBUG: {}", model_main_status));
                output.push_str(&format!("\nDEBUG: Script execution time: {}ms", start_time.elapsed().as_millis()));
                output.push_str(&format!("\nDEBUG: Draw commands generated: {}", draw_commands.len()));

                // Debug: Print each draw command
                for (i, cmd) in draw_commands.iter().enumerate() {
                    println!("Draw command {}: {:?}", i, cmd);
                }

                let debug_state = if enable_debugging {
                    self.debug_state.lock().unwrap().clone()
                } else {
                    None
                };

                LuaExecutionResult {
                    success: true,
                    output,
                    error: None,
                    execution_time_ms: start_time.elapsed().as_millis() as u64,
                    draw_commands,
                    debug_state,
                    makerjs_json: self.makerjs_json.lock().unwrap().clone(),
                    makerjs_svg: self.makerjs_svg.lock().unwrap().clone(),
                }
            }
            Err(err) => {
                println!("Script execution failed: {}", err);
                LuaExecutionResult {
                    success: false,
                    output: String::new(),
                    error: Some(format!("Lua execution error: {}", err)),
                    execution_time_ms: start_time.elapsed().as_millis() as u64,
                    draw_commands: Vec::new(),
                    debug_state: None,
                    makerjs_json: None,
                    makerjs_svg: None,
                }
            }
        }
    }



    fn setup_debug_hook(&self) {
        // For now, we'll implement a simple line-by-line debugging approach
        // In a full implementation, we would use Lua's debug.sethook function
        // to set up proper debugging hooks
        println!("🔧 Debug hook setup - breakpoint debugging enabled");

        // Get breakpoints
        let breakpoints = self.breakpoints.lock().unwrap();
        if !breakpoints.is_empty() {
            println!("🔴 Active breakpoints:");
            for bp in breakpoints.iter() {
                if bp.enabled {
                    if let Some(ref condition) = bp.condition {
                        println!("  📍 Line {}: {} (condition: {})",
                            bp.line_number, bp.file_path, condition);
                    } else {
                        println!("  📍 Line {}: {}",
                            bp.line_number, bp.file_path);
                    }
                } else {
                    println!("  ⚪ Line {}: {} (disabled)",
                        bp.line_number, bp.file_path);
                }
            }

            // Add a simple message to the output buffer about breakpoints
            let mut output_buffer = self.output_buffer.lock().unwrap();
            output_buffer.push(format!("🔧 Debugging enabled with {} breakpoint(s)",
                breakpoints.iter().filter(|bp| bp.enabled).count()));
        } else {
            println!("ℹ️  No breakpoints set");
        }
    }

    pub fn check_breakpoint(&self, line: u32, file: &str) -> Option<BreakpointInfo> {
        let breakpoints = self.breakpoints.lock().unwrap();

        for bp in breakpoints.iter() {
            if bp.enabled && bp.line_number == line && bp.file_path == file {
                // Check condition if present
                if let Some(ref condition) = bp.condition {
                    // For now, we'll assume conditions are always true
                    // In a full implementation, we would evaluate the condition
                    println!("Breakpoint hit at line {} with condition: {}", line, condition);
                } else {
                    println!("Breakpoint hit at line {}", line);
                }

                return Some(bp.clone());
            }
        }

        None
    }

    fn inject_breakpoint_checks(&self, script: &str) -> String {
        let breakpoints = self.breakpoints.lock().unwrap();
        if breakpoints.is_empty() {
            return script.to_string();
        }

        let lines: Vec<&str> = script.lines().collect();
        let mut result = Vec::new();

        for (line_num, line) in lines.iter().enumerate() {
            let current_line = (line_num + 1) as u32;

            // Check if there's a breakpoint on this line
            let has_breakpoint = breakpoints.iter().any(|bp|
                bp.enabled && bp.line_number == current_line
            );

            if has_breakpoint {
                // Add a debug print before the original line
                result.push(format!("print('🔴 BREAKPOINT HIT: Line {} - {}')", current_line, line.trim()));

                // Add the original line
                result.push(line.to_string());

                // Add a small pause simulation (just a print for now)
                result.push("print('   ⏸️  Execution paused at breakpoint')".to_string());
            } else {
                // Add the original line as-is
                result.push(line.to_string());
            }
        }

        result.join("\n")
    }
}
