-- Bottom Face Positioning Test
-- This script tests the corrected positioning of bottom face operations in sweep operations
-- It creates operations on both top and bottom faces to verify correct Z positioning
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  -- Initialize ADekoLib
  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()

  print("=== Bottom Face Positioning Test ===")
  print("Panel: " .. X .. "mm x " .. Y .. "mm x " .. materialThickness .. "mm")

  -- TOP FACE OPERATIONS (for comparison)
  print("\n--- TOP FACE OPERATIONS ---")

  -- Top face drilling (6mm endmill, 5mm depth)
  print("Creating top face drilling operation...")
  <PERSON><PERSON>set<PERSON>ayer("6MM")
  G.setFace("top")
  G.setThickness(-5) -- 5mm depth from top surface
  G.circle({25, 20}, 3) -- 6mm diameter circle at (25,20)

  -- Top face grooving (8mm endmill, 3mm depth)
  print("Creating top face grooving operation...")
  G.setLayer("8MM")
  G.setFace("top")
  G.setThickness(-3) -- 3mm depth from top surface
  G.rectangle({40, 30}, {60, 50}) -- Rectangle groove

  -- BOTTOM FACE OPERATIONS (the ones we fixed)
  print("\n--- BOTTOM FACE OPERATIONS ---")

  -- Bottom face drilling (6mm endmill, 5mm depth)
  print("Creating bottom face drilling operation...")
  G.setLayer("6MM")
  G.setFace("bottom")
  G.setThickness(-5) -- 5mm depth from bottom surface (should extend upward)
  G.circle({75, 20}, 3) -- 6mm diameter circle at (75,20)

  -- Bottom face grooving (8mm endmill, 3mm depth)
  print("Creating bottom face grooving operation...")
  G.setLayer("8MM")
  G.setFace("bottom")
  G.setThickness(-3) -- 3mm depth from bottom surface (should extend upward)
  G.rectangle({40, 50}, {60, 70}) -- Rectangle groove

  -- Bottom face pocketing (10mm endmill, 4mm depth)
  print("Creating bottom face pocketing operation...")
  G.setLayer("10MM")
  G.setFace("bottom")
  G.setThickness(-4) -- 4mm depth from bottom surface (should extend upward)
  G.circle({25, 60}, 8) -- 16mm diameter pocket at (25,60)

  print("\n=== Test Summary ===")
  print("Top face operations:")
  print("  - Drill at (25,20): 6mm dia, 5mm depth from top surface (Z=0 to Z=-5mm)")
  print("  - Groove at (40,30)-(60,50): 3mm depth from top surface (Z=0 to Z=-3mm)")
  print("")
  print("Bottom face operations (FIXED POSITIONING):")
  print("  - Drill at (75,20): 6mm dia, 5mm depth from bottom surface (Z=-" .. materialThickness .. "mm to Z=-" .. (materialThickness-5) .. "mm)")
  print("  - Groove at (40,50)-(60,70): 3mm depth from bottom surface (Z=-" .. materialThickness .. "mm to Z=-" .. (materialThickness-3) .. "mm)")
  print("  - Pocket at (25,60): 16mm dia, 4mm depth from bottom surface (Z=-" .. materialThickness .. "mm to Z=-" .. (materialThickness-4) .. "mm)")
  print("")
  print("Expected behavior:")
  print("  - Top face operations should cut downward from Z=0")
  print("  - Bottom face operations should cut upward from Z=-" .. materialThickness .. "mm (door thickness)")
  print("  - All operations should be properly positioned in the 3D model")

  -- Test completed
  print("\n✅ Bottom face positioning test completed!")
  print("Load this in the Lua editor and check the 3D visualization to verify:")
  print("1. Bottom face operations are positioned correctly")
  print("2. They extend upward from the bottom surface")
  print("3. No operations extend beyond the door boundaries")

  return true
end

require "ADekoDebugMode"
