-- Bottom Face Positioning Test
-- This script tests the corrected positioning of bottom face operations in sweep operations
-- It creates operations on both top and bottom faces to verify correct Z positioning

-- Initialize ADekoLib
ADekoLib.start()
ADekoLib.showPoints(true)
ADekoLib.enableListing(true)

-- Set panel dimensions (100mm x 80mm x 20mm)
ADekoLib.makePart(100, 80)
print("=== Bottom Face Positioning Test ===")
print("Panel: 100mm x 80mm x 20mm")

-- Create the door panel (base geometry)
print("Creating door panel...")
ADekoLib.setLayer("PANEL")
ADekoLib.rect("door", 0, 0, 100, 80)

-- TOP FACE OPERATIONS (for comparison)
print("\n--- TOP FACE OPERATIONS ---")

-- Top face drilling (6mm endmill, 5mm depth)
print("Creating top face drilling operation...")
ADekoLib.layer("6MM")
ADekoLib.setFace("top")
ADekoLib.setThickness(-5) -- 5mm depth from top surface
ADekoLib.circle("top_drill", 25, 20, 3) -- 6mm diameter circle at (25,20)

-- Top face grooving (8mm endmill, 3mm depth)
print("Creating top face grooving operation...")
ADekoLib.layer("8MM")
ADekoLib.setFace("top")
ADekoLib.setThickness(-3) -- 3mm depth from top surface
ADekoLib.rect("top_groove", 40, 30, 60, 50) -- Rectangle groove

-- BOTTOM FACE OPERATIONS (the ones we fixed)
print("\n--- BOTTOM FACE OPERATIONS ---")

-- Bottom face drilling (6mm endmill, 5mm depth)
print("Creating bottom face drilling operation...")
ADekoLib.layer("6MM")
ADekoLib.setFace("bottom")
ADekoLib.setThickness(-5) -- 5mm depth from bottom surface (should extend upward)
ADekoLib.circle("bottom_drill", 75, 20, 3) -- 6mm diameter circle at (75,20)

-- Bottom face grooving (8mm endmill, 3mm depth)
print("Creating bottom face grooving operation...")
ADekoLib.layer("8MM")
ADekoLib.setFace("bottom")
ADekoLib.setThickness(-3) -- 3mm depth from bottom surface (should extend upward)
ADekoLib.rect("bottom_groove", 40, 50, 60, 70) -- Rectangle groove

-- Bottom face pocketing (10mm endmill, 4mm depth)
print("Creating bottom face pocketing operation...")
ADekoLib.layer("10MM")
ADekoLib.setFace("bottom")
ADekoLib.setThickness(-4) -- 4mm depth from bottom surface (should extend upward)
ADekoLib.circle("bottom_pocket", 25, 60, 8) -- 16mm diameter pocket at (25,60)

print("\n=== Test Summary ===")
print("Top face operations:")
print("  - Drill at (25,20): 6mm dia, 5mm depth from top surface (Z=0 to Z=-5mm)")
print("  - Groove at (40,30)-(60,50): 3mm depth from top surface (Z=0 to Z=-3mm)")
print("")
print("Bottom face operations (FIXED POSITIONING):")
print("  - Drill at (75,20): 6mm dia, 5mm depth from bottom surface (Z=-20mm to Z=-15mm)")
print("  - Groove at (40,50)-(60,70): 3mm depth from bottom surface (Z=-20mm to Z=-17mm)")
print("  - Pocket at (25,60): 16mm dia, 4mm depth from bottom surface (Z=-20mm to Z=-16mm)")
print("")
print("Expected behavior:")
print("  - Top face operations should cut downward from Z=0")
print("  - Bottom face operations should cut upward from Z=-20mm (door thickness)")
print("  - All operations should be properly positioned in the 3D model")

-- Test completed
print("\n✅ Bottom face positioning test completed!")
print("Load this in the Lua editor and check the 3D visualization to verify:")
print("1. Bottom face operations are positioned correctly")
print("2. They extend upward from the bottom surface")
print("3. No operations extend beyond the door boundaries")
